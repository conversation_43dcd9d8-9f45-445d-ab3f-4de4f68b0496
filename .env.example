# -----------------------------------------------------------------------------
# 基础配置
# -----------------------------------------------------------------------------
NEXT_PUBLIC_WEB_URL=http://localhost:3000
NEXT_PUBLIC_PROJECT_NAME=ShipSaaS

# -----------------------------------------------------------------------------
# Auth配置 (必填)
# https://authjs.dev/getting-started/installation?framework=Next.js
# AUTH_SECRET可以通过运行 `openssl rand -base64 32` 生成
# -----------------------------------------------------------------------------
AUTH_SECRET=

# Google认证 (可选)
# https://authjs.dev/getting-started/providers/google
AUTH_GOOGLE_ID=
AUTH_GOOGLE_SECRET=
NEXT_PUBLIC_AUTH_GOOGLE_ENABLED=true
NEXT_PUBLIC_AUTH_GOOGLE_ONE_TAP_ENABLED=false

# Github认证 (可选)
# https://authjs.dev/getting-started/providers/github
AUTH_GITHUB_ID=
AUTH_GITHUB_SECRET=
NEXT_PUBLIC_AUTH_GITHUB_ENABLED=true

# GitHub API Token for repository access (必填)
# https://docs.github.com/en/authentication/keeping-your-account-and-data-secure/creating-a-personal-access-token
GITHUB_API_TOKEN=

# -----------------------------------------------------------------------------
# Stripe支付配置 (必填)
# https://docs.stripe.com/keys
# https://docs.stripe.com/checkout/embedded/quickstart
# 本地监听命令：stripe listen --forward-to localhost:3000/api/stripe/webhook
# -----------------------------------------------------------------------------
STRIPE_PUBLIC_KEY=
STRIPE_PRIVATE_KEY=
STRIPE_WEBHOOK_SECRET=

# -----------------------------------------------------------------------------
# 数据库配置 (必填)
# 支持 Neon 或 Supabase
# -----------------------------------------------------------------------------
# Neon数据库
# https://neon.tech/docs/guides/nextjs
DATABASE_URL=

# Supabase数据库 (可选)
# https://supabase.com/docs/guides/getting-started/quickstarts/nextjs
# SUPABASE_URL=
# SUPABASE_ANON_KEY= 