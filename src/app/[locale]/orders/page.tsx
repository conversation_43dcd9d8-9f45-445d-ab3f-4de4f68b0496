"use client";

import { useEffect, useState } from "react";
import { useTranslations } from "next-intl";
import { useSession } from "next-auth/react";
import { format } from "date-fns";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { CheckCircle, Loader2 } from "lucide-react";
import { GitHubInviteModal } from "@/components/ui/github-invite-modal";

interface Order {
  id: number;
  orderNo: string;
  amount: number;
  status: string;
  createdAt: string;
  productName: string;
  currency: string;
  paidAt: string | null;
}

export default function OrdersPage() {
  const t = useTranslations("orders");
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [activatingOrders, setActivatingOrders] = useState<Set<string>>(new Set());
  const [githubModalOpen, setGithubModalOpen] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const { data: session } = useSession();

  useEffect(() => {
    const fetchOrders = async () => {
      try {
        const response = await fetch("/api/orders");
        if (!response.ok) {
          throw new Error("Failed to fetch orders");
        }
        const data = await response.json();
        setOrders(data);
      } catch (error) {
        console.error("Error fetching orders:", error);
      } finally {
        setLoading(false);
      }
    };

    if (session) {
      fetchOrders();
    }
  }, [session]);

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "paid":
        return "bg-green-500";
      case "pending":
        return "bg-yellow-500";
      case "failed":
        return "bg-red-500";
      case "expired":
        return "bg-gray-500";
      default:
        return "bg-gray-500";
    }
  };

  const formatAmount = (amount: number, currency: string) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currency || "USD",
    }).format(amount / 100);
  };

  const getStatusText = (status: string) => {
    const statusKey = status.toLowerCase();
    // Use type assertion for dynamic keys
    return t(`orderDetails.status.${statusKey}` as any);
  };

  const handleActivateOrder = async (orderNo: string) => {
    // First activate the order
    try {
      setActivatingOrders(prev => new Set(prev).add(orderNo));

      const response = await fetch("/api/orders/activate", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ orderNo }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to activate order");
      }

      await response.json();

      // Update the order status in the local state
      setOrders(prevOrders =>
        prevOrders.map(order =>
          order.orderNo === orderNo
            ? { ...order, status: 'activated' }
            : order
        )
      );

      // Find the order and open GitHub invite modal
      const order = orders.find(o => o.orderNo === orderNo);
      if (order) {
        setSelectedOrder({ ...order, status: 'activated' });
        setGithubModalOpen(true);
      }

    } catch (error) {
      console.error("Error activating order:", error);
      alert(t("orderDetails.activationError"));
    } finally {
      setActivatingOrders(prev => {
        const newSet = new Set(prev);
        newSet.delete(orderNo);
        return newSet;
      });
    }
  };

  const handleOpenGithubInvite = (order: Order) => {
    setSelectedOrder(order);
    setGithubModalOpen(true);
  };

  const handleCloseGithubModal = () => {
    setGithubModalOpen(false);
    setSelectedOrder(null);
  };

  const canActivateOrder = (order: Order) => {
    return order.status.toLowerCase() === 'paid' && order.paidAt;
  };

  const isOrderActivating = (orderNo: string) => {
    return activatingOrders.has(orderNo);
  };

  if (loading) {
    return (
      <div className="container mx-auto py-8">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-6">{t("title")}</h1>
      <p className="text-gray-600 mb-8">{t("description")}</p>

      {orders.length === 0 ? (
        <p className="text-center text-gray-500">{t("noOrders")}</p>
      ) : (
        <div className="grid gap-4">
          {orders.map((order) => (
            <div
              key={order.orderNo}
              className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow"
            >
              <div className="flex justify-between items-start mb-4">
                <div>
                  <h3 className="text-lg font-semibold mb-2">
                    {order.productName || t("orderDetails.purchase")}
                  </h3>
                  <p className="text-sm text-gray-500">
                    {t("orderDetails.orderId")}: {order.orderNo}
                  </p>
                </div>
                <Badge className={getStatusColor(order.status)}>
                  {getStatusText(order.status)}
                </Badge>
              </div>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                <div>
                  <p className="text-gray-500">{t("orderDetails.amount")}</p>
                  <p className="font-medium">
                    {formatAmount(order.amount, order.currency)}
                  </p>
                </div>
                <div>
                  <p className="text-gray-500">{t("orderDetails.orderDate")}</p>
                  <p className="font-medium">
                    {format(new Date(order.createdAt), "PPP")}
                  </p>
                </div>
                {order.paidAt && (
                  <div>
                    <p className="text-gray-500">{t("orderDetails.paidDate")}</p>
                    <p className="font-medium">
                      {format(new Date(order.paidAt), "PPP")}
                    </p>
                  </div>
                )}
              </div>

              {/* Activation Button for Paid Orders */}
              {canActivateOrder(order) && (
                <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                      <CheckCircle className="w-4 h-4 text-green-500" />
                      <span>{t("orderDetails.paidDate")}: {format(new Date(order.paidAt!), "PPP")}</span>
                    </div>
                    <Button
                      onClick={() => handleActivateOrder(order.orderNo)}
                      disabled={isOrderActivating(order.orderNo)}
                      className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white font-semibold px-6 py-2 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                    >
                      {isOrderActivating(order.orderNo) ? (
                        <>
                          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                          {t("orderDetails.activating")}
                        </>
                      ) : (
                        <>
                          <CheckCircle className="w-4 h-4 mr-2" />
                          {t("orderDetails.activateOrder")}
                        </>
                      )}
                    </Button>
                  </div>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                    {t("orderDetails.activationDescription")}
                  </p>
                </div>
              )}

              {/* Already Activated Status */}
              {order.status.toLowerCase() === 'activated' && (
                <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2 text-sm text-green-600 dark:text-green-400">
                      <CheckCircle className="w-4 h-4" />
                      <span className="font-medium">{t("orderDetails.activated")}</span>
                    </div>
                    <Button
                      onClick={() => handleOpenGithubInvite(order)}
                      variant="outline"
                      size="sm"
                      className="border-blue-200 text-blue-600 hover:bg-blue-50 dark:border-blue-700 dark:text-blue-400 dark:hover:bg-blue-900/20"
                    >
                      <CheckCircle className="w-4 h-4 mr-2" />
                      {t("orderDetails.githubInvite")}
                    </Button>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      )}

      {/* GitHub Invite Modal */}
      {selectedOrder && (
        <GitHubInviteModal
          isOpen={githubModalOpen}
          onClose={handleCloseGithubModal}
          orderNo={selectedOrder.orderNo}
          productName={selectedOrder.productName || t("orderDetails.purchase")}
        />
      )}
    </div>
  );
}
