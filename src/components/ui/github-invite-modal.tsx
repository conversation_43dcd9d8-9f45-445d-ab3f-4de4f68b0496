"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle 
} from "@/components/ui/dialog";
import { Github, Loader2, CheckCircle, AlertCircle } from "lucide-react";
import { useTranslations } from "next-intl";

/**
 * GitHub Invite Modal Props
 * GitHub 邀请弹窗属性
 */
interface GitHubInviteModalProps {
  /** Whether the modal is open / 弹窗是否打开 */
  isOpen: boolean;
  /** Function to close the modal / 关闭弹窗的函数 */
  onClose: () => void;
  /** Order number to send invitation for / 要发送邀请的订单号 */
  orderNo: string;
  /** Product name for display / 显示的产品名称 */
  productName: string;
}

/**
 * GitHub Invite Modal Component
 * GitHub 邀请弹窗组件
 */
export function GitHubInviteModal({ 
  isOpen, 
  onClose, 
  orderNo, 
  productName 
}: GitHubInviteModalProps) {
  const t = useTranslations("orders");
  const [githubUsername, setGithubUsername] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState(false);
  const [invitationUrl, setInvitationUrl] = useState("");

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!githubUsername.trim()) {
      setError(t("githubInvite.usernameRequired"));
      return;
    }

    // Validate GitHub username format
    const githubUsernameRegex = /^[a-z\d](?:[a-z\d]|-(?=[a-z\d])){0,38}$/i;
    if (!githubUsernameRegex.test(githubUsername.trim())) {
      setError(t("githubInvite.invalidUsername"));
      return;
    }

    setIsLoading(true);
    setError("");

    try {
      const response = await fetch("/api/github/invite", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          orderNo,
          githubUsername: githubUsername.trim(),
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to send invitation");
      }

      setSuccess(true);
      setInvitationUrl(data.invitationUrl || "");
      
    } catch (error: any) {
      console.error("Error sending GitHub invitation:", error);
      setError(error.message || t("githubInvite.sendError"));
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    setGithubUsername("");
    setError("");
    setSuccess(false);
    setInvitationUrl("");
    onClose();
  };

  const handleOpenInvitation = () => {
    if (invitationUrl) {
      window.open(invitationUrl, "_blank");
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Github className="w-5 h-5" />
            {t("githubInvite.title")}
          </DialogTitle>
          <DialogDescription>
            {t("githubInvite.description", { productName })}
          </DialogDescription>
        </DialogHeader>

        {!success ? (
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="github-username">
                {t("githubInvite.usernameLabel")}
              </Label>
              <div className="relative">
                <Github className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                <Input
                  id="github-username"
                  type="text"
                  placeholder={t("githubInvite.usernamePlaceholder")}
                  value={githubUsername}
                  onChange={(e) => setGithubUsername(e.target.value)}
                  className="pl-10"
                  disabled={isLoading}
                />
              </div>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                {t("githubInvite.usernameHint")}
              </p>
            </div>

            {error && (
              <div className="flex items-center gap-2 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                <AlertCircle className="w-4 h-4 text-red-500" />
                <p className="text-sm text-red-700 dark:text-red-400">{error}</p>
              </div>
            )}

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={isLoading}
              >
                {t("githubInvite.cancel")}
              </Button>
              <Button
                type="submit"
                disabled={isLoading || !githubUsername.trim()}
                className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    {t("githubInvite.sending")}
                  </>
                ) : (
                  <>
                    <Github className="w-4 h-4 mr-2" />
                    {t("githubInvite.sendInvite")}
                  </>
                )}
              </Button>
            </DialogFooter>
          </form>
        ) : (
          <div className="space-y-4">
            <div className="flex items-center gap-3 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
              <CheckCircle className="w-6 h-6 text-green-500" />
              <div>
                <p className="font-medium text-green-800 dark:text-green-200">
                  {t("githubInvite.successTitle")}
                </p>
                <p className="text-sm text-green-700 dark:text-green-300">
                  {t("githubInvite.successMessage", { username: githubUsername })}
                </p>
              </div>
            </div>

            <div className="space-y-2">
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {t("githubInvite.nextSteps")}
              </p>
              <ul className="text-xs text-gray-500 dark:text-gray-400 space-y-1 ml-4">
                <li>• {t("githubInvite.step1")}</li>
                <li>• {t("githubInvite.step2")}</li>
                <li>• {t("githubInvite.step3")}</li>
              </ul>
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={handleClose}>
                {t("githubInvite.close")}
              </Button>
              {invitationUrl && (
                <Button onClick={handleOpenInvitation}>
                  <Github className="w-4 h-4 mr-2" />
                  {t("githubInvite.viewInvitation")}
                </Button>
              )}
            </DialogFooter>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
