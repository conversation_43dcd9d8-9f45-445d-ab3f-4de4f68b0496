"use client";

import { useState } from "react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { CheckCircle, Github, CreditCard, Calendar, DollarSign } from "lucide-react";
import { format } from "date-fns";
import { GitHubInviteModal } from "@/components/ui/github-invite-modal";

/**
 * GitHub Invite Demo Component
 * GitHub 邀请演示组件
 */
export function GitHubInviteDemo() {
  const [githubModalOpen, setGithubModalOpen] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<any>(null);

  // Mock activated order data / 模拟已激活订单数据
  const mockOrder = {
    id: 1,
    orderNo: "ORD-2024-001",
    amount: 2999, // $29.99
    status: "activated",
    createdAt: "2024-01-15T10:30:00Z",
    productName: "Pro Plan - Monthly",
    currency: "USD",
    paidAt: "2024-01-15T10:35:00Z",
  };

  const formatAmount = (amount: number, currency: string) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currency || "USD",
    }).format(amount / 100);
  };

  const handleOpenGithubInvite = () => {
    setSelectedOrder(mockOrder);
    setGithubModalOpen(true);
  };

  const handleCloseGithubModal = () => {
    setGithubModalOpen(false);
    setSelectedOrder(null);
  };

  return (
    <div className="min-h-screen bg-background p-8">
      <div className="container mx-auto max-w-4xl">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">GitHub Invite Demo</h1>
          <p className="text-gray-600 dark:text-gray-400">
            Demonstration of GitHub repository invitation functionality for activated orders
          </p>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 hover:shadow-xl transition-all duration-300 border border-gray-200 dark:border-gray-700">
          <div className="flex justify-between items-start mb-4">
            <div>
              <h3 className="text-lg font-semibold mb-2 text-gray-900 dark:text-white">
                {mockOrder.productName}
              </h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Order ID: {mockOrder.orderNo}
              </p>
            </div>
            <Badge className="bg-green-500 hover:bg-green-600 text-white border-0">
              Activated
            </Badge>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm mb-6">
            <div className="flex items-center gap-2">
              <DollarSign className="w-4 h-4 text-gray-500" />
              <div>
                <p className="text-gray-500 dark:text-gray-400">Amount</p>
                <p className="font-medium text-gray-900 dark:text-white">
                  {formatAmount(mockOrder.amount, mockOrder.currency)}
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Calendar className="w-4 h-4 text-gray-500" />
              <div>
                <p className="text-gray-500 dark:text-gray-400">Order Date</p>
                <p className="font-medium text-gray-900 dark:text-white">
                  {format(new Date(mockOrder.createdAt), "PPP")}
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <CreditCard className="w-4 h-4 text-gray-500" />
              <div>
                <p className="text-gray-500 dark:text-gray-400">Paid Date</p>
                <p className="font-medium text-gray-900 dark:text-white">
                  {format(new Date(mockOrder.paidAt), "PPP")}
                </p>
              </div>
            </div>
          </div>

          {/* Activated Status with GitHub Access */}
          <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2 text-sm text-green-600 dark:text-green-400">
                <CheckCircle className="w-4 h-4" />
                <span className="font-medium">Order Activated - Features Unlocked</span>
              </div>
              <Button
                onClick={handleOpenGithubInvite}
                className="bg-gradient-to-r from-gray-900 to-gray-700 hover:from-gray-800 hover:to-gray-600 text-white font-semibold px-6 py-2 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
              >
                <Github className="w-4 h-4 mr-2" />
                GitHub Access
              </Button>
            </div>
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
              Get access to the private GitHub repository with your purchase.
            </p>
          </div>
        </div>

        {/* Instructions */}
        <div className="mt-8 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
          <h3 className="text-lg font-semibold mb-3 text-blue-900 dark:text-blue-100">
            How GitHub Access Works
          </h3>
          <div className="space-y-2 text-sm text-blue-800 dark:text-blue-200">
            <p>1. <strong>Activate Order:</strong> First, your order must be in "activated" status</p>
            <p>2. <strong>Click GitHub Access:</strong> Click the "GitHub Access" button to open the invitation modal</p>
            <p>3. <strong>Enter Username:</strong> Provide your GitHub username (e.g., "octocat")</p>
            <p>4. <strong>Receive Invitation:</strong> You'll receive a repository collaboration invitation</p>
            <p>5. <strong>Accept Invitation:</strong> Check your GitHub notifications and accept the invitation</p>
            <p>6. <strong>Access Repository:</strong> Start accessing the private repository content</p>
          </div>
        </div>

        {/* API Information */}
        <div className="mt-6 bg-gray-50 dark:bg-gray-800/50 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
          <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-gray-100">
            Technical Implementation
          </h3>
          <div className="space-y-2 text-sm text-gray-700 dark:text-gray-300">
            <p><strong>API Endpoint:</strong> <code className="bg-gray-200 dark:bg-gray-700 px-2 py-1 rounded">POST /api/github/invite</code></p>
            <p><strong>GitHub API:</strong> Uses GitHub's Collaborators API to send repository invitations</p>
            <p><strong>Permission Level:</strong> "pull" access (read-only) to the repository</p>
            <p><strong>Repository:</strong> ShipSaaSCo/shipsaas-starter (private repository)</p>
            <p><strong>Authentication:</strong> Requires valid session and activated order</p>
          </div>
        </div>

        {/* GitHub Invite Modal */}
        {selectedOrder && (
          <GitHubInviteModal
            isOpen={githubModalOpen}
            onClose={handleCloseGithubModal}
            orderNo={selectedOrder.orderNo}
            productName={selectedOrder.productName}
          />
        )}
      </div>
    </div>
  );
}
