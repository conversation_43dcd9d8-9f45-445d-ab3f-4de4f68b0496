/**
 * Hero Section Component / 英雄区组件
 *
 * @description The main hero section component for landing pages, featuring animated backgrounds,
 * gradient text effects, call-to-action buttons, and responsive design. This component serves
 * as the primary focal point for user engagement and conversion.
 * @description 落地页的主要英雄区组件，具有动画背景、渐变文本效果、行动号召按钮和响应式设计。
 * 此组件作为用户参与和转化的主要焦点。
 *
 * @features
 * - Animated background with gradient effects and floating particles
 * - Responsive typography with gradient text styling
 * - Call-to-action buttons with hover animations
 * - Framer Motion animations for smooth entrance effects
 * - Full viewport height with centered content alignment
 * - Dark/light theme support with seamless transitions
 *
 * @特性
 * - 具有渐变效果和浮动粒子的动画背景
 * - 具有渐变文本样式的响应式排版
 * - 具有悬停动画的行动号召按钮
 * - Framer Motion 动画实现流畅的入场效果
 * - 全视口高度和内容居中对齐
 * - 深色/浅色主题支持和无缝过渡
 *
 * @layout
 * - Responsive design (mobile-first approach)
 * - Full viewport height minus header (min-h-[calc(100vh-4rem)])
 * - Centered content alignment with proper spacing
 * - Background animations and decorative elements
 *
 * @布局
 * - 响应式设计（移动优先方法）
 * - 全视口高度减去头部（min-h-[calc(100vh-4rem)]）
 * - 内容居中对齐和适当间距
 * - 背景动画和装饰元素
 *
 * @accessibility
 * - Semantic HTML structure with proper heading hierarchy
 * - ARIA labels for interactive elements
 * - Keyboard navigation support for all interactive elements
 * - Screen reader friendly with descriptive text
 * - High contrast support for better visibility
 *
 * @无障碍性
 * - 具有适当标题层次结构的语义化 HTML 结构
 * - 交互元素的 ARIA 标签
 * - 所有交互元素的键盘导航支持
 * - 具有描述性文本的屏幕阅读器友好
 * - 高对比度支持以提高可见性
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */

"use client";

import { Button } from "@/components/ui/button";
import { motion } from "framer-motion";

/**
 * Props interface for the Hero component
 * Hero 组件的属性接口
 */
interface HeroProps {
  /**
   * Hero section content data
   * 英雄区内容数据
   */
  hero: {
    /**
     * Main headline text - should be compelling and action-oriented
     * 主标题文本 - 应该引人注目且面向行动
     * @example "All-in-One SaaS Launch Solution"
     */
    title: string;

    /**
     * Supporting subtitle text - provides additional context
     * 支持性副标题文本 - 提供额外上下文
     * @example "Smart automation, instant deployment, accelerate your business growth"
     */
    subtitle: string;

    /**
     * Detailed description text - explains the value proposition
     * 详细描述文本 - 解释价值主张
     * @example "Ship SaaS Demo delivers a ready-to-use SaaS template..."
     */
    description: string;

    /**
     * Call-to-action button configuration
     * 行动号召按钮配置
     */
    cta: {
      /**
       * Primary CTA button text - main conversion action
       * 主要 CTA 按钮文本 - 主要转化行动
       * @example "Try for Free Now"
       */
      primary: string;

      /**
       * Secondary CTA button text - alternative action
       * 次要 CTA 按钮文本 - 替代行动
       * @example "See Pricing"
       */
      secondary: string;
    };

    /**
     * Optional special offer badge configuration
     * 可选的特殊优惠标签配置
     */
    badge?: {
      /**
       * Badge text content
       * 标签文本内容
       * @example "Special GIF: 20% off"
       */
      text: string;
      /**
       * Badge emoji or icon
       * 标签表情符号或图标
       * @example "🔥"
       */
      icon?: string;
    };

    /**
     * Optional social proof configuration
     * 可选的社会证明配置
     */
    socialProof?: {
      /**
       * Social proof text
       * 社会证明文本
       * @example "90+ makers ship faster with MkSaaS"
       */
      text: string;
      /**
       * Number of user avatars to show
       * 显示的用户头像数量
       * @default 6
       */
      avatarCount?: number;
    };
  };
}

/**
 * Hero - Main landing page hero section component
 * Hero - 主要落地页英雄区组件
 *
 * @description Renders the primary hero section with animated backgrounds, gradient text,
 * and call-to-action buttons. Optimized for conversion and user engagement.
 * @description 渲染具有动画背景、渐变文本和行动号召按钮的主要英雄区。
 * 针对转化和用户参与进行了优化。
 *
 * @param props - Hero component props containing content data
 * @param props.hero - Hero section content including title, subtitle, description, and CTAs
 * @returns JSX.Element - The rendered hero section
 *
 * @example Basic usage / 基本用法
 * ```tsx
 * <Hero
 *   hero={{
 *     title: "Welcome to ShipSaaS",
 *     subtitle: "Build and launch your SaaS faster",
 *     description: "Complete SaaS template with authentication, payments, and more",
 *     cta: {
 *       primary: "Get Started",
 *       secondary: "Learn More"
 *     }
 *   }}
 * />
 * ```
 *
 * @example With internationalization / 国际化使用
 * ```tsx
 * const { hero } = useTranslations('hero');
 * <Hero hero={hero} />
 * ```
 */
export function Hero({ hero }: HeroProps) {
  return (
    <section
      id="hero"
      className="relative flex items-center justify-center min-h-[calc(100vh-4rem)] w-full py-16 md:py-24 lg:py-32 overflow-hidden bg-background"
      aria-label="Hero section"
    >
      {/* Simplified Background System */}
      <div className="absolute inset-0 -z-10">
        {/* Clean gradient background */}
        <div className="absolute inset-0 bg-gradient-to-b from-background via-background/95 to-background" />
        {/* Subtle radial gradient for depth */}
        <div className="absolute inset-0 bg-[radial-gradient(circle_600px_at_50%_300px,rgba(99,102,241,0.05),transparent)] dark:bg-[radial-gradient(circle_600px_at_50%_300px,rgba(99,102,241,0.08),transparent)]" />
      </div>

      <div className="container px-4 md:px-6 mx-auto max-w-5xl">
        <div className="flex flex-col items-center justify-center space-y-8 text-center">
          {/* Special Offer Badge - Conditional Rendering */}
          {hero.badge && (
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-orange-100 to-red-100 dark:from-orange-900/30 dark:to-red-900/30 border border-orange-200 dark:border-orange-700 rounded-full text-sm font-medium text-orange-800 dark:text-orange-200"
            >
              {hero.badge.icon && <span>{hero.badge.icon}</span>}
              <span>{hero.badge.text}</span>
              {hero.badge.icon && <span>{hero.badge.icon}</span>}
            </motion.div>
          )}

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="space-y-6 max-w-4xl mx-auto"
          >
            <h1 className="text-4xl font-bold tracking-tight sm:text-5xl md:text-6xl lg:text-7xl bg-clip-text text-transparent bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 dark:from-white dark:via-gray-100 dark:to-white leading-tight">
              {hero.title}
            </h1>
            <p className="text-lg text-gray-600 md:text-xl lg:text-2xl dark:text-gray-300 max-w-3xl mx-auto leading-relaxed">
              {hero.subtitle}
            </p>
            <p className="text-base text-gray-500 md:text-lg dark:text-gray-400 max-w-2xl mx-auto leading-relaxed">
              {hero.description}
            </p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2, ease: "easeOut" }}
            className="flex flex-col sm:flex-row items-center justify-center gap-4"
          >
            <Button
              size="lg"
              className="min-w-[160px] bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white rounded-lg px-8 h-12 text-base font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
            >
              {hero.cta.primary}
            </Button>
            <Button
              variant="outline"
              size="lg"
              className="min-w-[160px] rounded-lg px-8 h-12 text-base font-semibold border border-gray-300 hover:bg-gray-50 dark:border-gray-600 dark:hover:bg-gray-800 transition-all duration-300 transform hover:scale-105"
            >
              {hero.cta.secondary}
            </Button>
          </motion.div>

          {/* User Avatars and Social Proof - Conditional Rendering */}
          {hero.socialProof && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="flex flex-col items-center gap-4 mt-8"
            >
              {/* User Avatars */}
              <div className="flex items-center -space-x-2">
                {Array.from({ length: hero.socialProof?.avatarCount || 6 }, (_, index) => {
                  const colors = [
                    'from-blue-400 to-blue-600',
                    'from-green-400 to-green-600',
                    'from-purple-400 to-purple-600',
                    'from-orange-400 to-orange-600',
                    'from-pink-400 to-pink-600',
                    'from-indigo-400 to-indigo-600',
                    'from-cyan-400 to-cyan-600',
                    'from-red-400 to-red-600'
                  ];
                  const avatarCount = hero.socialProof?.avatarCount || 6;
                  const isLast = index === avatarCount - 1;
                  const letter = isLast ? '+' : String.fromCharCode(65 + index); // A, B, C, ... or +

                  return (
                    <div
                      key={index}
                      className={`w-10 h-10 rounded-full bg-gradient-to-r ${colors[index % colors.length]} border-2 border-white dark:border-gray-800 flex items-center justify-center text-white text-sm font-semibold`}
                    >
                      {letter}
                    </div>
                  );
                })}
              </div>

              {/* Social Proof Text */}
              <p className="text-sm text-gray-600 dark:text-gray-400 font-medium">
                {hero.socialProof.text}
              </p>
            </motion.div>
          )}
        </div>
      </div>
    </section>
  );
}
