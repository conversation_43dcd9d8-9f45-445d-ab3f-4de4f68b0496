# 多计划GitHub邀请功能实现

## 功能概述

根据不同的价格计划，用户激活订单时会被邀请到不同的GitHub仓库，实现分层访问控制。

## 仓库映射规则

### 计划与仓库对应关系

| 计划名称 | 英文名称 | 中文名称 | 目标仓库 | 说明 |
|---------|---------|---------|----------|------|
| Starter | Starter | 基础版 | `ShipSaaSCo/shipsaas-starter` | 基础功能和模板 |
| Pro | Pro | 专业版 | `ShipSaaSCo/shipsaas-standard` | 高级功能和集成 |
| Enterprise | Enterprise | 旗舰版 | `ShipSaaSCo/shipsaas-enterprise` | 企业级功能和支持 |

### 识别逻辑

系统通过订单的 `productName` 字段来识别计划类型：

```typescript
const getRepositoryName = (productName: string): string => {
  const productLower = productName.toLowerCase();
  
  // Check for Starter plan / 检查入门版
  if (productLower.includes('starter') || productLower.includes('基础版')) {
    return 'shipsaas-starter';
  }
  
  // Check for Pro plan / 检查专业版
  if (productLower.includes('pro') || productLower.includes('专业版')) {
    return 'shipsaas-standard';
  }
  
  // Check for Enterprise plan / 检查旗舰版
  if (productLower.includes('enterprise') || productLower.includes('旗舰版')) {
    return 'shipsaas-enterprise';
  }
  
  // Default to starter if no match / 默认使用入门版
  return 'shipsaas-starter';
};
```

## 技术实现

### 1. API 修改 (`src/app/api/github/invite/route.ts`)

#### 动态仓库选择
```typescript
const repositoryName = getRepositoryName(order.productName || '');
const githubApiUrl = `https://api.github.com/repos/ShipSaaSCo/${repositoryName}/collaborators/${githubUsername}`;
```

#### 增强的响应数据
```typescript
return NextResponse.json({
  success: true,
  message: 'GitHub invitation sent successfully',
  invitationUrl: `https://github.com/ShipSaaSCo/${repositoryName}/invitations`,
  repositoryName: repositoryName
});
```

#### 订单详情更新
```typescript
await prisma.order.update({
  where: { orderNo: orderNo },
  data: {
    orderDetail: JSON.stringify({
      ...JSON.parse(order.orderDetail || '{}'),
      githubUsername: githubUsername,
      repositoryName: repositoryName,
      invitationSentAt: new Date().toISOString(),
    }),
    updatedAt: new Date(),
  },
});
```

### 2. 前端组件更新

#### GitHub邀请弹窗 (`src/components/ui/github-invite-modal.tsx`)
- 添加 `repositoryName` 状态管理
- 在成功页面显示具体的仓库信息
- 更新清理函数包含仓库名称重置

#### 演示组件 (`src/components/demo/MultiPlanGitHubDemo.tsx`)
- 展示不同计划的订单
- 显示每个计划对应的目标仓库
- 提供仓库映射说明

## 产品名称格式

### 支持的产品名称格式
系统支持以下产品名称格式的识别：

#### Starter 计划
- `Starter - monthly`
- `Starter - yearly`
- `基础版 - 月付`
- `基础版 - 年付`

#### Pro 计划
- `Pro - monthly`
- `Pro - yearly`
- `专业版 - 月付`
- `专业版 - 年付`

#### Enterprise 计划
- `Enterprise - monthly`
- `Enterprise - yearly`
- `旗舰版 - 月付`
- `旗舰版 - 年付`

### 容错机制
- **大小写不敏感**: 使用 `toLowerCase()` 进行比较
- **关键词匹配**: 使用 `includes()` 进行部分匹配
- **默认回退**: 无法识别时默认使用 `shipsaas-starter`

## 安全性和权限

### GitHub Token 权限要求
Token 需要对所有目标仓库具有管理员权限：
- `ShipSaaSCo/shipsaas-starter`
- `ShipSaaSCo/shipsaas-standard`
- `ShipSaaSCo/shipsaas-enterprise`

### 权限验证
- **订单所有权**: 验证用户拥有该订单
- **激活状态**: 确保订单已激活
- **仓库访问**: 确保Token对目标仓库有权限

## 用户体验

### 1. 透明的仓库信息
- 成功页面显示具体的仓库名称
- 演示页面展示仓库映射关系
- 清晰的计划与仓库对应说明

### 2. 一致的操作流程
- 所有计划使用相同的激活流程
- 统一的GitHub邀请弹窗界面
- 相同的成功反馈和后续指导

### 3. 错误处理
- 产品名称无法识别时的警告日志
- 仓库访问失败时的具体错误信息
- 用户友好的错误提示

## 演示和测试

### 演示页面
访问 `http://localhost:3001/en/demo/multi-plan-github` 查看：
- 不同计划的订单展示
- 仓库映射关系说明
- 激活流程演示

### 测试场景
1. **Starter计划测试**
   - 产品名称: "Starter - monthly"
   - 预期仓库: `shipsaas-starter`

2. **Pro计划测试**
   - 产品名称: "Pro - monthly"
   - 预期仓库: `shipsaas-standard`

3. **Enterprise计划测试**
   - 产品名称: "Enterprise - monthly"
   - 预期仓库: `shipsaas-enterprise`

4. **中文计划测试**
   - 产品名称: "专业版 - 月付"
   - 预期仓库: `shipsaas-standard`

5. **未知计划测试**
   - 产品名称: "Unknown Plan"
   - 预期仓库: `shipsaas-starter` (默认)

## 日志和监控

### 关键日志点
```typescript
// 仓库选择日志
console.log(`Sending GitHub invitation to ${githubUsername} for repository ShipSaaSCo/${repositoryName} (Product: ${order.productName})`);

// 成功日志
console.log(`GitHub invitation sent successfully to ${githubUsername} for order ${orderNo} (Repository: ${repositoryName})`);

// 警告日志
console.warn(`Unknown product name: ${productName}, defaulting to starter repository`);
```

### 监控指标
- 不同计划的激活成功率
- 仓库邀请发送成功率
- 未识别产品名称的频率

## 扩展性

### 添加新计划
要添加新的价格计划，需要：

1. **更新仓库映射函数**
```typescript
if (productLower.includes('premium') || productLower.includes('高级版')) {
  return 'shipsaas-premium';
}
```

2. **创建对应的GitHub仓库**
3. **确保Token对新仓库有权限**
4. **更新文档和演示**

### 配置化改进
未来可以考虑将仓库映射配置化：
```typescript
const REPOSITORY_MAPPING = {
  starter: 'shipsaas-starter',
  pro: 'shipsaas-standard',
  enterprise: 'shipsaas-enterprise',
  premium: 'shipsaas-premium'
};
```

## 总结

多计划GitHub邀请功能成功实现了：

1. ✅ **分层访问控制**: 不同计划访问不同仓库
2. ✅ **智能识别**: 支持中英文产品名称识别
3. ✅ **容错机制**: 无法识别时的默认处理
4. ✅ **透明信息**: 用户可见具体的仓库信息
5. ✅ **统一体验**: 保持一致的操作流程
6. ✅ **完整日志**: 便于调试和监控

这个功能为不同价格计划的用户提供了差异化的GitHub仓库访问权限，实现了真正的分层服务。
