{"template": "flux-ai-template", "theme": "dark", "header": {"logo": "ShipSaas", "nav": {"benefit": "Benefits", "features": "Features", "stats": "Insights", "pricing": "Pricing", "testimonial": "Testimonials", "faq": "FAQ"}, "cta": {"login": "<PERSON><PERSON>", "signup": "Free Trial"}, "userMenu": {"myOrders": "My Orders", "signOut": "Sign Out", "profile": "Profile"}}, "hero": {"title": "Make Your AI SaaS Product in a weekend", "subtitle": "The complete Next.js boilerplate for building profitable SaaS, packed with AI, auth, payments, i18n, newsletter, dashboard, blog, docs, blocks, themes, SEO and more.", "description": "Everything you need to launch your SaaS quickly and efficiently. Built with modern technologies and best practices.", "cta": {"primary": "Get MkSaaS", "secondary": "See <PERSON><PERSON>"}, "badge": {"text": "Special GIF: 20% off", "icon": "🔥"}, "socialProof": {"text": "90+ makers ship faster with MkSaaS", "avatarCount": 6}}, "benefit": {"title": "Intelligent Power, Ultimate Experience", "subtitle": "Six core advantages to empower every SaaS project", "benefits": [{"title": "Automated Subscription Management", "description": "Built-in Stripe automation for subscriptions and billing. Effortless recurring revenue.", "icon": "automation"}, {"title": "Seamless Multi-Platform Integration", "description": "Connect with major cloud and third-party services. Fast integration, flexible expansion.", "icon": "integration"}, {"title": "i18n & SEO Optimization", "description": "Multi-language switching and SEO best practices. Go global with ease.", "icon": "globe"}, {"title": "Modern UI & Superior Experience", "description": "Stunning Shadcn UI and Tailwind. Beautiful, smooth, and user-centric interfaces.", "icon": "design"}, {"title": "Open Source & Extensible", "description": "Fully open source, easy to extend and customize for any business scenario.", "icon": "code"}, {"title": "Security, Compliance & Customer Success", "description": "Enterprise-grade security, compliance, and expert support for your ongoing success.", "icon": "security"}]}, "stats": {"title": "Data-Driven Value", "subtitle": "Hundreds of projects launched, industry-leading satisfaction", "stats": [{"value": "500+", "label": "Projects Launched", "description": "The trusted choice of SaaS teams"}, {"value": "98%", "label": "Customer Satisfaction", "description": "Top ratings and repeat customers"}, {"value": "20+", "label": "Integrated Platforms", "description": "Supports major cloud and third-party services"}, {"value": "<2 days", "label": "Avg. Launch Time", "description": "Rapid delivery, fast business validation"}]}, "pricing": {"title": "Transparent & Flexible Pricing", "subtitle": "Choose the right plan for every stage of SaaS growth", "perMonth": "/month", "contactUs": "Contact Us", "getStarted": "Get Started", "buyNow": "Free Trial", "pleaseLogin": "Please login before making a purchase", "frequencies": {"monthly": "Monthly", "yearly": "Yearly"}, "plans": [{"name": "Starter", "monthlyPrice": 99, "yearlyPrice": 990, "originalPrice": 199, "description": "For solo developers and startups. Build SaaS prototypes fast.", "features": ["Automated subscription management", "i18n & SEO optimization", "Modern UI component library", "Open source & customizable", "Comprehensive documentation"]}, {"name": "Pro", "monthlyPrice": 199, "yearlyPrice": 1990, "originalPrice": 299, "description": "For growing teams. Multi-platform integration and advanced features.", "features": ["All Basic features", "Seamless multi-platform integration", "Custom domain & branding", "Advanced analytics", "Community & technical support"]}, {"name": "Enterprise", "monthlyPrice": 299, "yearlyPrice": 2990, "originalPrice": 399, "description": "For enterprise SaaS. Meet complex business and compliance needs.", "features": ["All Pro features", "Multi-tenant & permission management", "API keys & webhook support", "Security & compliance", "Dedicated customer success manager"]}]}, "testimonial": {"title": "Customer Success Stories", "subtitle": "Real feedback, proven SaaS growth", "testimonials": [{"content": "ShipSaas enabled our team to launch from zero to live in record time. Automated subscriptions and integrations boosted our efficiency.", "author": {"name": "<PERSON>", "title": "Tech Partner", "company": "CloudRise", "image": "/testimonials/1.jpg"}}, {"content": "Beautiful UI, rich features, and great docs. Our team expanded and customized quickly, and support was always responsive.", "author": {"name": "<PERSON>", "title": "Product Manager", "company": "Starsoft", "image": "/testimonials/2.jpg"}}, {"content": "Security and compliance give our enterprise clients peace of mind. ShipSaas is a trustworthy SaaS foundation.", "author": {"name": "<PERSON>", "title": "Operations Director", "company": "Zhilian Group", "image": "/testimonials/3.jpg"}}]}, "faq": {"title": "Frequently Asked Questions", "subtitle": "Your top questions about <PERSON><PERSON><PERSON><PERSON> answered", "faqs": [{"question": "How do I launch my SaaS product quickly?", "answer": "Just clone the repo, set up environment variables, and deploy. Our docs guide you every step of the way."}, {"question": "Which third-party platforms are supported?", "answer": "Supports Vercel, Cloudflare, Stripe, and more. We’re always expanding integrations."}, {"question": "How is data security and compliance handled?", "answer": "Industry-standard security, GDPR compliance, and enterprise-grade data protection."}, {"question": "Does it support i18n and localization?", "answer": "Yes, built-in i18n makes it easy to switch languages and go global."}, {"question": "How do I get support if I have issues?", "answer": "Comprehensive docs, community support, and a dedicated manager (Enterprise) ensure peace of mind."}]}, "auth": {"signInTitle": "Sign in to your account", "signInDescription": "Access your account to continue", "signInWithGoogle": "Sign in with Google", "signInWithGithub": "Sign in with GitHub", "signingIn": "Signing in...", "oauthError": "An error occurred during sign in. Please try again.", "authError": "Authentication error. Please try again.", "signIn": "Sign In", "email": "Email address", "password": "Password", "confirmPassword": "Confirm Password", "noAccount": "Don't have an account? Sign up", "alreadyHaveAccount": "Already have an account? Sign in", "orContinueWith": "Or continue with", "invalidCredentials": "Invalid email or password", "signInError": "An error occurred during sign in. Please try again.", "signUp": "Sign Up", "signingUp": "Signing up...", "createAccount": "Create a new account", "signUpError": "An error occurred during sign up. Please try again.", "passwordsDoNotMatch": "Passwords do not match", "signUpSuccess": "Account created successfully! Signing you in..."}, "cta": {"title": "Start Your Smart SaaS Journey", "subtitle": "Experience efficient, intelligent, and automated SaaS development and operations", "cta": {"primary": "Try ShipSaas Free", "secondary": "Request a Custom Plan"}}, "footer": {"newsletter": {"title": "Subscribe for Updates", "description": "Get SaaS development tips, product news, and exclusive offers to fuel your growth.", "placeholder": "Enter your email", "subscribe": "Subscribe", "subscribeAria": "Subscribe to newsletter"}, "quickLinks": {"title": "Quick Links", "home": "Home", "features": "Features", "pricing": "Pricing", "blog": "Blog", "about": "About Us"}, "resources": {"title": "Resources", "documentation": "Docs", "tutorials": "Tutorials", "community": "Community", "support": "Support Center", "api": "API Reference"}, "company": {"title": "Company", "about": "About ShipSaas", "careers": "Careers", "press": "Press", "contact": "Contact Us", "partners": "Partners"}, "social": {"title": "Follow Us", "github": "GitHub Repository", "twitter": "Twitter", "discord": "Join <PERSON>"}, "legal": {"title": "Legal", "privacy": "Privacy Policy", "terms": "Terms of Service", "cookies": "<PERSON><PERSON>"}, "contact": {"title": "Contact Info", "address": "Shanghai, China", "email": "<EMAIL>", "hours": "Mon-Fri 9:00-18:00"}, "theme": {"light": "Light Mode", "dark": "Dark Mode", "toggle": "Toggle Theme"}, "copyright": "© 2024 ShipSaas. All rights reserved.", "tagline": "Smarter, faster SaaS launch for every team"}, "orders": {"title": "My Orders", "description": "View all your order history and status", "noOrders": "No orders found", "orderDetails": {"purchase": "Purchase", "orderId": "Order ID", "amount": "Amount", "orderDate": "Order Date", "paidDate": "Paid <PERSON>", "activateOrder": "Activate Order", "activating": "Activating...", "activated": "Activated", "activationSuccess": "Order activated successfully!", "activationError": "Failed to activate order. Please try again.", "activationDescription": "Click to activate your order and unlock your purchased features.", "activatedDescription": "Order has been activated and GitHub repository access has been granted.", "githubInvite": "GitHub Access", "status": {"paid": "Paid", "pending": "Pending", "failed": "Failed", "expired": "Expired", "activated": "Activated"}}, "githubInvite": {"title": "GitHub Repository Access", "description": "Enter your GitHub username to receive repository access for {productName}", "usernameLabel": "GitHub Username", "usernamePlaceholder": "Enter your GitHub username", "usernameHint": "Example: octocat", "usernameRequired": "GitHub username is required", "invalidUsername": "Invalid GitHub username format", "usernameNotFound": "GitHub username not found. Please check the username and try again.", "insufficientPermissions": "Insufficient permissions. Please contact support for assistance.", "alreadyCollaborator": "User is already a collaborator or invitation already exists.", "sendInvite": "Send Invitation", "sending": "Sending...", "cancel": "Cancel", "sendError": "Failed to send invitation. Please try again.", "successTitle": "Invitation Sent!", "successMessage": "GitHub repository invitation has been sent to {username}", "autoRedirect": "Redirecting to GitHub notifications in 2 seconds...", "nextSteps": "Next steps:", "step1": "Check your GitHub notifications", "step2": "Accept the repository invitation", "step3": "Start accessing the repository", "close": "Close", "viewNotifications": "View GitHub Notifications"}}, "profile": {"title": "Profile", "subtitle": "View and manage your personal information", "personalInfo": "Personal Information", "uuid": "Unique ID", "email": "Email", "nickname": "Nickname", "registrationDate": "Registration Date", "signinMethod": "Sign-in Method", "lastSigninIp": "Last Sign-in IP", "unknown": "Unknown"}}