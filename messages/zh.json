{"template": "flux-ai-template", "theme": "dark", "header": {"logo": "ShipSaas", "nav": {"benefit": "产品优势", "features": "功能亮点", "stats": "数据洞察", "pricing": "价格方案", "testimonial": "客户评价", "faq": "常见问题"}, "cta": {"login": "登录", "signup": "免费试用"}, "userMenu": {"myOrders": "我的订单", "signOut": "退出登录", "profile": "个人资料"}}, "hero": {"title": "一站式SaaS上线解决方案", "subtitle": "智能自动化，极速发布，助力业务高效增长", "description": "ShipSaas 提供开箱即用的SaaS模板，集成自动化订阅、国际化、现代UI与多平台集成，极致提升开发与运营效率。无需繁琐配置，专注创新与增长。", "cta": {"primary": "立即免费体验", "secondary": "了解价格方案"}}, "benefit": {"title": "智能驱动，极致体验", "subtitle": "六大核心优势，赋能每一个SaaS项目", "benefits": [{"title": "自动化订阅管理", "description": "内置Stripe自动化订阅与账单，轻松实现持续营收。", "icon": "automation"}, {"title": "多平台无缝集成", "description": "支持主流云平台与第三方服务，快速对接，灵活扩展。", "icon": "integration"}, {"title": "国际化与SEO优化", "description": "多语言切换与SEO最佳实践，助力产品全球化。", "icon": "globe"}, {"title": "现代UI与极致体验", "description": "采用Shadcn UI与Tailwind，界面美观，交互流畅，提升用户满意度。", "icon": "design"}, {"title": "开源高扩展性", "description": "全开源架构，支持二次开发与深度定制，满足多样化业务需求。", "icon": "code"}, {"title": "安全合规与客户成功", "description": "数据安全与合规保障，专业团队与社区支持，助力客户持续成功。", "icon": "security"}]}, "stats": {"title": "数据见证价值", "subtitle": "数百项目选择，客户满意度持续领先", "stats": [{"value": "500+", "label": "成功上线项目", "description": "众多SaaS团队的共同选择"}, {"value": "98%", "label": "客户满意度", "description": "高分好评，持续复购"}, {"value": "20+", "label": "集成平台数", "description": "支持主流云与第三方服务"}, {"value": "<2天", "label": "平均上线周期", "description": "极速交付，快速验证业务"}]}, "pricing": {"title": "透明灵活的定价方案", "subtitle": "按需选择，助力每个阶段的SaaS成长", "perMonth": "/月", "contactUs": "联系我们", "getStarted": "立即开始", "buyNow": "免费试用", "pleaseLogin": "请先登录后再进行购买", "frequencies": {"monthly": "按月付费", "yearly": "按年付费"}, "plans": [{"name": "基础版", "monthlyPrice": 99, "yearlyPrice": 990, "originalPrice": 199, "description": "适合个人开发者和初创团队，快速搭建SaaS原型。", "features": ["自动化订阅管理", "多语言与SEO优化", "现代UI组件库", "开源可定制", "详细开发文档"]}, {"name": "专业版", "monthlyPrice": 199, "yearlyPrice": 1990, "originalPrice": 299, "description": "适合成长型团队，支持多平台集成与高级扩展。", "features": ["基础版全部功能", "多平台无缝集成", "自定义域名与品牌", "高级数据分析", "社区与技术支持"]}, {"name": "旗舰版", "monthlyPrice": 299, "yearlyPrice": 2990, "originalPrice": 399, "description": "适合企业级SaaS项目，满足复杂业务与安全合规需求。", "features": ["专业版全部功能", "多租户与权限管理", "API密钥与Webhook支持", "安全合规保障", "专属客户成功经理"]}]}, "testimonial": {"title": "客户成功故事", "subtitle": "真实反馈，见证SaaS高效成长", "testimonials": [{"content": "Ship SaaS Demo 让我们团队实现了从0到1的极速上线，自动化订阅和多平台集成极大提升了运营效率。", "author": {"name": "陈伟", "title": "技术合伙人", "company": "云启科技", "image": "/testimonials/1.jpg"}}, {"content": "界面美观、功能丰富，文档完善，支持团队快速扩展和二次开发，客户支持响应及时。", "author": {"name": "李娜", "title": "产品经理", "company": "星辰软件", "image": "/testimonials/2.jpg"}}, {"content": "数据安全和合规性让我们企业客户非常放心，Ship SaaS Demo 是值得信赖的SaaS基础设施。", "author": {"name": "王磊", "title": "运营总监", "company": "智联集团", "image": "/testimonials/3.jpg"}}]}, "faq": {"title": "常见问题解答", "subtitle": "关于Ship SaaS Demo的常见疑问", "faqs": [{"question": "如何快速上线我的SaaS产品？", "answer": "只需克隆项目，配置环境变量，一键部署即可上线，详细文档全程指导。"}, {"question": "支持哪些第三方平台集成？", "answer": "支持Vercel、Cloudflare、Stripe等主流云与支付平台，持续扩展中。"}, {"question": "数据安全和合规如何保障？", "answer": "采用业界标准安全措施，支持GDPR等合规要求，企业级数据保护。"}, {"question": "是否支持多语言和国际化？", "answer": "内置i18n国际化方案，轻松切换多语言界面，助力全球化。"}, {"question": "遇到问题如何获得支持？", "answer": "提供详细文档、社区支持及专属客户经理（旗舰版），保障无忧使用。"}]}, "auth": {"signInTitle": "登录您的账户", "signInDescription": "登录您的账户以继续", "signInWithGoogle": "使用 Google 账号登录", "signInWithGithub": "使用 GitHub 账号登录", "signingIn": "正在登录...", "oauthError": "登录过程中发生错误，请重试。", "authError": "认证错误，请重试。", "signIn": "登录", "email": "邮箱地址", "password": "密码", "confirmPassword": "确认密码", "noAccount": "还没有账号？注册一个", "alreadyHaveAccount": "已有账号？立即登录", "orContinueWith": "或使用以下方式继续", "invalidCredentials": "邮箱或密码不正确", "signInError": "登录时发生错误，请重试", "signUp": "注册", "signingUp": "正在注册...", "createAccount": "创建新账号", "signUpError": "注册时发生错误，请重试", "passwordsDoNotMatch": "两次输入的密码不一致", "signUpSuccess": "注册成功！正在自动登录..."}, "cta": {"title": "开启智能SaaS之旅", "subtitle": "立即体验高效、智能、自动化的SaaS开发与运营", "cta": {"primary": "免费试用Ship SaaS Demo", "secondary": "咨询专属方案"}}, "footer": {"newsletter": {"title": "订阅最新动态", "description": "获取SaaS开发干货、产品更新与专属优惠，助力业务持续增长。", "placeholder": "输入您的邮箱", "subscribe": "订阅", "subscribeAria": "订阅邮件动态"}, "quickLinks": {"title": "快速链接", "home": "首页", "features": "功能亮点", "pricing": "价格方案", "blog": "博客", "about": "关于我们"}, "resources": {"title": "资源", "documentation": "开发文档", "tutorials": "使用教程", "community": "开发者社区", "support": "支持中心", "api": "API参考"}, "company": {"title": "公司", "about": "关于Ship SaaS Demo", "careers": "加入我们", "press": "媒体报道", "contact": "联系我们", "partners": "合作伙伴"}, "social": {"title": "关注我们", "github": "GitHub开源仓库", "twitter": "Twitter官方账号", "discord": "加入Discord社区"}, "legal": {"title": "法律信息", "privacy": "隐私政策", "terms": "服务条款", "cookies": "<PERSON><PERSON>设置"}, "contact": {"title": "联系方式", "address": "中国·上海", "email": "<EMAIL>", "hours": "工作日 9:00-18:00"}, "theme": {"light": "浅色模式", "dark": "深色模式", "toggle": "切换主题"}, "copyright": "© 2024 Ship SaaS Demo。保留所有权利。", "tagline": "让SaaS上线更智能、更高效"}, "orders": {"title": "我的订单", "description": "查看您的所有订单记录和状态", "noOrders": "暂无订单记录", "orderDetails": {"purchase": "购买", "orderId": "订单号", "amount": "金额", "orderDate": "下单时间", "paidDate": "支付时间", "status": {"paid": "已支付", "pending": "待支付", "failed": "支付失败", "expired": "已过期"}}}, "profile": {"title": "个人资料", "subtitle": "查看和管理您的个人信息", "personalInfo": "个人信息", "uuid": "唯一标识", "email": "邮箱", "nickname": "昵称", "registrationDate": "注册时间", "signinMethod": "登录方式", "lastSigninIp": "最后登录IP", "unknown": "未知"}}